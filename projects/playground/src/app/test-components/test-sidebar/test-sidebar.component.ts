import { Component } from '@angular/core';
import { SidebarComponent, SidebarSize } from '../../../../../play-comp-library/src/public-api';
import { IconComponent } from '../../../../../play-comp-library/src/public-api';
import { ButtonComponent } from '../../../../../play-comp-library/src/public-api';

@Component({
  selector: 'app-test-sidebar',
  standalone: true,
  imports: [SidebarComponent, IconComponent, ButtonComponent],
  templateUrl: './test-sidebar.component.html',
  styleUrl: './test-sidebar.component.scss'
})
export class TestSidebarComponent {
  // Size variants for testing
  sizes: SidebarSize[] = ['xsmall', 'small', 'medium', 'large', 'xlarge'];
  
  // State management for different sidebar instances
  sidebarStates = {
    xsmall: false,
    small: false,
    medium: false,
    large: false,
    xlarge: false,
    leftInside: false,
    rightInside: false,
    leftOutside: false,
    rightOutside: false,
    static: false
  };

  // Toggle methods for each sidebar
  onSizeToggle(size: SidebarSize, isCollapsed: boolean): void {
    this.sidebarStates[size] = isCollapsed;
    console.log(`${size} sidebar toggled:`, isCollapsed);
  }

  onLeftInsideToggle(isCollapsed: boolean): void {
    this.sidebarStates.leftInside = isCollapsed;
    console.log('Left inside sidebar toggled:', isCollapsed);
  }

  onRightInsideToggle(isCollapsed: boolean): void {
    this.sidebarStates.rightInside = isCollapsed;
    console.log('Right inside sidebar toggled:', isCollapsed);
  }

  onLeftOutsideToggle(isCollapsed: boolean): void {
    this.sidebarStates.leftOutside = isCollapsed;
    console.log('Left outside sidebar toggled:', isCollapsed);
  }

  onRightOutsideToggle(isCollapsed: boolean): void {
    this.sidebarStates.rightOutside = isCollapsed;
    console.log('Right outside sidebar toggled:', isCollapsed);
  }

  // Demo navigation items
  navItems = [
    { icon: 'Home', label: 'Dashboard', active: true },
    { icon: 'Users', label: 'Users', active: false },
    { icon: 'FileText', label: 'Projects', active: false },
    { icon: 'ChartBar', label: 'Analytics', active: false },
    { icon: 'Settings', label: 'Settings', active: false }
  ];

  toolItems = [
    { icon: 'Palette', label: 'Themes', active: true },
    { icon: 'Layers', label: 'Layers', active: false },
    { icon: 'Image', label: 'Assets', active: false },
    { icon: 'Type', label: 'Typography', active: false },
    { icon: 'Share2', label: 'Export', active: false }
  ];
}
