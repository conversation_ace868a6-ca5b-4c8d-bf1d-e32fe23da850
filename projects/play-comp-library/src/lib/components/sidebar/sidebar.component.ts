import { CommonModule } from '@angular/common';
import { ChangeDetectionStrategy, ChangeDetectorRef, Component, Input, Output, EventEmitter, OnInit, OnChanges, SimpleChanges, ViewEncapsulation } from '@angular/core';
import { ButtonComponent } from '../button/button.component';

// Sidebar Size Variants
export type SidebarSize = 'xsmall' | 'small' | 'medium' | 'large' | 'xlarge';

@Component({
  selector: 'ava-sidebar',
  imports: [CommonModule, ButtonComponent],
  templateUrl: './sidebar.component.html',
  styleUrl: './sidebar.component.scss',
  changeDetection: ChangeDetectionStrategy.OnPush,
  encapsulation: ViewEncapsulation.None,
})
export class SidebarComponent implements OnInit, OnChanges {
  // Size variant input - when provided, overrides width and collapsedWidth
  @Input() size: SidebarSize = 'medium';

  // Manual width overrides (optional - size variant takes precedence)
  @Input() width: string = '';
  @Input() collapsedWidth: string = '';

  @Input() height: string = '100vh';
  @Input() hoverAreaWidth: string = '10px';
  @Input() showCollapseButton: boolean = false;
  @Input() buttonVariant: 'inside' | 'outside' = 'inside';
  @Input() showHeader: boolean = true;
  @Input() showFooter: boolean = true;
  @Input() isCollapsed: boolean = false;
  @Input() position: 'left' | 'right' = 'left';

  @Output() collapseToggle = new EventEmitter<boolean>();

  private _isCollapsed = false;

  // Size configuration mapping
  private readonly sizeConfig = {
    xsmall: { width: '200px', collapsedWidth: '40px' },
    small: { width: '240px', collapsedWidth: '50px' },
    medium: { width: '280px', collapsedWidth: '60px' },
    large: { width: '320px', collapsedWidth: '70px' },
    xlarge: { width: '360px', collapsedWidth: '80px' }
  };

  constructor(private cdr: ChangeDetectorRef) { }

  ngOnInit() {
    this._isCollapsed = this.isCollapsed;
  }

  ngOnChanges(changes: SimpleChanges) {
    if (changes['isCollapsed'] && !changes['isCollapsed'].firstChange) {
      this._isCollapsed = this.isCollapsed;
      this.cdr.markForCheck();
    }
  }

  toggleCollapse(): void {
    this._isCollapsed = !this._isCollapsed;
    this.collapseToggle.emit(this._isCollapsed);
    this.cdr.markForCheck();
  }

  // Computed width based on size variant or manual override
  get computedWidth(): string {
    return this.width || this.sizeConfig[this.size].width;
  }

  // Computed collapsed width based on size variant or manual override
  get computedCollapsedWidth(): string {
    return this.collapsedWidth || this.sizeConfig[this.size].collapsedWidth;
  }

  get sidebarWidth(): string {
    return this._isCollapsed ? this.computedCollapsedWidth : this.computedWidth;
  }

  get collapsed(): boolean {
    return this._isCollapsed;
  }

  get isRightPositioned(): boolean {
    return this.position === 'right';
  }

  get collapseButtonIcon(): string {
    if (this.position === 'right') {
      return this._isCollapsed ? 'ArrowLeft' : 'ArrowRight';
    }
    return this._isCollapsed ? 'ArrowRight' : 'ArrowLeft';
  }

  // CSS classes for size variants
  get sidebarClasses(): string {
    const classes = [
      'ava-sidebar',
      `ava-sidebar--${this.size}`,
      this.collapsed ? 'collapsed' : '',
      this.isRightPositioned ? 'right-positioned' : ''
    ];
    return classes.filter(Boolean).join(' ');
  }

  // Computed styles for dynamic CSS properties
  get computedStyles(): Record<string, string> {
    return {
      '--sidebar-computed-width': this.computedWidth,
      '--sidebar-computed-collapsed-width': this.computedCollapsedWidth,
    };
  }
}
