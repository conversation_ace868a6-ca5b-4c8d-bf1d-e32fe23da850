<div class="ava-sidebar-container"
     [class.right-positioned]="isRightPositioned"
     [class.outside-button]="buttonVariant === 'outside'"
     [style.--sidebar-width]="sidebarWidth"
     [style.--hover-area-width]="hoverAreaWidth">
  <!-- Main Sidebar -->

  <div
    [class]="sidebarClasses"
    [ngStyle]="computedStyles"
    [style.width]="sidebarWidth"
    [style.height]="height"
  >
    <!-- Header Section -->

    <div class="sidebar-header" *ngIf="showHeader">
      <div class="header-content" *ngIf="!collapsed">
        <ng-content select="[slot=header]"></ng-content>
      </div>

      <!-- Inside Button Variant -->

      <div
        class="header-controls"
        *ngIf="showCollapseButton && buttonVariant === 'inside'"
      >
        <ava-button
        [iconName]="collapseButtonIcon"
        iconPosition="only"
        size="small"
        (click)="toggleCollapse()"
        variant="primary"
        ></ava-button>
      </div>
    </div>

    <!-- Main Content Section -->

    <div class="sidebar-content">
      <ng-content select="[slot=content]"></ng-content>
    </div>

    <!-- Footer Section -->

    <div class="sidebar-footer" *ngIf="showFooter">
      <ng-content select="[slot=footer]"></ng-content>
    </div>
  </div>

  <!-- Outside Button Variant - Hover Area -->
  <div
    class="hover-area"
    [class.right-positioned]="isRightPositioned"
    [class.left-positioned]="!isRightPositioned"
    *ngIf="showCollapseButton && buttonVariant === 'outside'"
  >
    <div class="hover-area-content">
      <ava-button
        variant="primary"
        [iconName]="collapseButtonIcon"
        iconPosition="only"
        size="small"
        (click)="toggleCollapse()"
      ></ava-button>
    </div>
  </div>
</div>
