:root {
    /* =======================
       SIDEBAR CORE PROPERTIES
       ======================= */
    --sidebar-border: 1px solid var(--color-border-default);
    --sidebar-background: var(--color-surface-primary);
    --sidebar-transition: all 0.3s ease;
    --sidebar-border-radius: var(--global-border-radius-md);

    /* =======================
       SIDEBAR SIZE VARIANTS
       ======================= */

    /* XSmall Sidebar - Compact for mobile/small screens */
    --sidebar-size-xsm-width: 200px;
    --sidebar-size-xsm-collapsed-width: 40px;
    --sidebar-size-xsm-padding: var(--global-spacing-2);
    --sidebar-size-xsm-header-padding: var(--global-spacing-2) var(--global-spacing-2);
    --sidebar-size-xsm-content-padding: var(--global-spacing-2);
    --sidebar-size-xsm-footer-padding: var(--global-spacing-2) var(--global-spacing-2);
    --sidebar-size-xsm-nav-item-height: 32px;
    --sidebar-size-xsm-nav-item-padding: var(--global-spacing-1) var(--global-spacing-2);
    --sidebar-size-xsm-nav-item-font-size: var(--global-font-size-sm);
    --sidebar-size-xsm-icon-size: 16px;

    /* Small Sidebar - For tablets/medium screens */
    --sidebar-size-sm-width: 240px;
    --sidebar-size-sm-collapsed-width: 50px;
    --sidebar-size-sm-padding: var(--global-spacing-3);
    --sidebar-size-sm-header-padding: var(--global-spacing-3) var(--global-spacing-3);
    --sidebar-size-sm-content-padding: var(--global-spacing-3);
    --sidebar-size-sm-footer-padding: var(--global-spacing-3) var(--global-spacing-3);
    --sidebar-size-sm-nav-item-height: 36px;
    --sidebar-size-sm-nav-item-padding: var(--global-spacing-2) var(--global-spacing-3);
    --sidebar-size-sm-nav-item-font-size: var(--global-font-size-sm);
    --sidebar-size-sm-icon-size: 18px;

    /* Medium Sidebar - Standard size (default) */
    --sidebar-size-md-width: 280px;
    --sidebar-size-md-collapsed-width: 60px;
    --sidebar-size-md-padding: var(--global-spacing-4);
    --sidebar-size-md-header-padding: var(--global-spacing-4) var(--global-spacing-4);
    --sidebar-size-md-content-padding: var(--global-spacing-4);
    --sidebar-size-md-footer-padding: var(--global-spacing-4) var(--global-spacing-4);
    --sidebar-size-md-nav-item-height: 40px;
    --sidebar-size-md-nav-item-padding: var(--global-spacing-2) var(--global-spacing-4);
    --sidebar-size-md-nav-item-font-size: var(--global-font-size-base);
    --sidebar-size-md-icon-size: 20px;

    /* Large Sidebar - For desktop screens */
    --sidebar-size-lg-width: 320px;
    --sidebar-size-lg-collapsed-width: 70px;
    --sidebar-size-lg-padding: var(--global-spacing-5);
    --sidebar-size-lg-header-padding: var(--global-spacing-5) var(--global-spacing-5);
    --sidebar-size-lg-content-padding: var(--global-spacing-5);
    --sidebar-size-lg-footer-padding: var(--global-spacing-5) var(--global-spacing-5);
    --sidebar-size-lg-nav-item-height: 44px;
    --sidebar-size-lg-nav-item-padding: var(--global-spacing-3) var(--global-spacing-5);
    --sidebar-size-lg-nav-item-font-size: var(--global-font-size-lg);
    --sidebar-size-lg-icon-size: 22px;

    /* XLarge Sidebar - For wide screens */
    --sidebar-size-xlg-width: 360px;
    --sidebar-size-xlg-collapsed-width: 80px;
    --sidebar-size-xlg-padding: var(--global-spacing-6);
    --sidebar-size-xlg-header-padding: var(--global-spacing-6) var(--global-spacing-6);
    --sidebar-size-xlg-content-padding: var(--global-spacing-6);
    --sidebar-size-xlg-footer-padding: var(--global-spacing-6) var(--global-spacing-6);
    --sidebar-size-xlg-nav-item-height: 48px;
    --sidebar-size-xlg-nav-item-padding: var(--global-spacing-3) var(--global-spacing-6);
    --sidebar-size-xlg-nav-item-font-size: var(--global-font-size-xl);
    --sidebar-size-xlg-icon-size: 24px;
}
